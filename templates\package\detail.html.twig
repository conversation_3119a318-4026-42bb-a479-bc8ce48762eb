{% extends 'base.html.twig' %}

{% block title %}Suivi package - {{ package.id }}{% endblock %}

{% block body %}
<style>
#table1 > tbody > tr > td, #table1 > thead > tr > th {
    text-align: center;
    vertical-align: middle;
}
thead tr.entetes th {
    background-color: #004080;
    color: #fff;
    font-size: 0.85rem;
}

/* Styles pour les badges */
.old-place {
    background-color: #28a745;
    color: white;
}

.current-step {
    background-color:rgb(0, 17, 255);
    color: white;
}

.visited-place {
    background-color:rgb(73, 161, 255);
    color: white;
}

#table2-body > tr > td{
    white-space: nowrap;
}

#table2 > thead > tr > th {
    white-space: nowrap;
}

.custom-tooltip .tooltip-inner {
    white-space: normal;
    max-width: none;
}

#packageTabs .nav-link {
    color: #004080;
    background-color: #f8f9fa;
    border: none;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

#packageTabs .nav-link:hover {
    color: #fff;
    background-color:rgba(0, 64, 128, 0.8);
}

#packageTabs .nav-link.active {
    color: #fff;
    background-color: #004080;
    border: none;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.old-place.panier {
    background: repeating-linear-gradient(
    -55deg,
    #28a745,
    #28a745 10px,
    rgba(40, 167, 69, 0.65) 10px,
    rgba(40, 167, 69, 0.65) 20px
    );
}

.current-step.panier {
    background: repeating-linear-gradient(
    -55deg,
    rgb(0, 17, 255),
    rgb(0, 17, 255) 10px,
    rgb(0, 17, 255, 0.65) 10px,
    rgb(0, 17, 255, 0.65) 20px
    );
}

.visited-place.panier {
    background: repeating-linear-gradient(
    -55deg,
        rgb(73, 161, 255),
        rgb(73, 161, 255) 10px,
        rgba(73, 161, 255, 0.65) 10px,
        rgb(73, 161, 255, 0.65) 20px
    );
}

.panier {
    background: repeating-linear-gradient(
    -55deg,
    rgb(95, 95, 95),
    rgb(95, 95, 95) 10px,
    rgba(44, 44, 44, 0.65) 10px,
    rgba(44, 44, 44, 0.65) 20px
);
}
    

</style>
<div class="mt-3" style="margin: 0 1%;">
    <h4>Détail du package {{ package.id }}</h4>
    <hr class="my-2">
        <div class="row">
            <div class="col-md-3">
                <p><strong>Projet </strong> {{ package.getProjectRelation is not null and package.getProjectRelation.getOTP is not null ? package.getProjectRelation.getOTP : '' }}</p>
                <p><strong>Activité </strong> {{ package.Activity }}</p>
                <p><strong>Vérification </strong> {{ package.verif }}</p>
            </div>
            <div class="col-md-3">
                <p><strong>Date de réservation </strong> {{ package.ReservationDate ? package.ReservationDate|date('d/m/Y') : 'N/A' }}</p>
                <p><strong>Ex </strong> 
                {% if package.Ex != 'NO' %}
                    <span class="badge bg-danger">{{ package.Ex }}</span>
                    {% else %}
                        {{ package.Ex }}
                    {% endif %}
                <p><strong>Validation </strong> {{ package.valid }}</p>
            </div>
            <div class="col-md-3">
                <p><strong>Date de création </strong> {{ package.CreationDate ? package.CreationDate|date('d/m/Y') : 'N/A' }}</p>
                                                    {% set dmoCount = package.dmos|length %}
                                    {% if dmoCount > 2 %}
                                        <div class="dropdown">
                                            <span class="badge bg-secondary dropdown-toggle"
                                                id="dropdownMenuButton{{ package.id }}"
                                                data-bs-toggle="dropdown"
                                                aria-expanded="false">
                                                {{ dmoCount }} DMO
                                            </span>
                                            <ul class="dropdown-menu text-center shadow"
                                                aria-labelledby="dropdownMenuButton{{ package.id }}">
                                                {% for dmo in package.dmos %}
                                                    <li>
                                                        <a href="{{ path('app_dmo_show', {'id': dmo.getId()}) }}"
                                                           target="_blank"
                                                           class="badge bg-primary badge-hover"
                                                           style="text-decoration: none;">
                                                            {{ dmo.getDmoId() }}
                                                        </a>
                                                    </li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                    {% else %}
                                        {% for dmo in package.dmos %}
                                            <a href="{{ path('app_dmo_show', {'id': dmo.getId()}) }}"
                                               target="_blank"
                                               class="badge bg-primary badge-hover"
                                               style="text-decoration: none;">
                                                {{ dmo.getDmoId() }}
                                            </a>
                                        {% endfor %}
                                    {% endif %}
                </p>
                <p><strong>Description </strong> {{ package.description }}</p>
            </div>
            <div class="col-md-3">
                <p><strong>Propriétaire </strong> {{ package.owner }}</p>
                <p><strong>Documents </strong> <span class="badge bg-primary">{{ package.documents|length }}</span></p>
            </div>
        </div>
    <hr class="my-2">
    <ul class="nav nav-tabs border-0" id="packageTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="p1-tab" data-bs-toggle="tab" data-bs-target="#p-1" type="button" role="tab">Documents</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="p2-tab" data-bs-toggle="tab" data-bs-target="#p-2" type="button" role="tab">Suivi</button>
        </li>
    </ul>
    <div class="row tab-content">
        <div class="col-12 tab-pane fade show active" role="tabpanel" id="p-1">
            <table class="table table-hover mb-0" id="table1">
                <thead>
                    <tr class="entetes">
                        <th colspan="2">Référence</th>
                        <th colspan="2">Plan de Prod</th>
                        <th>Titre</th>
                        <th>Action</th>
                        <th>Inventaire</th>
                        <th>Doc Type</th>
                        <th>Pris Dans</th>
                        <th>Mat</th>
                        <th colspan="2">Commentaires</th>
                        <th colspan="2">Visas</th>
                    </tr>
                </thead>
                <tbody>
                {% for document in package.documents %}
                    <tr document-id="{{document.id}}">
                        <td>{{ document.reference|trim }}</td>
                        <td>
                            {{ document.refrev }}
                            {% if document.ex != 'NO' %}
                                <span style="color: red; font-weight: 500;"><sup>{{ document.ex }}</sup></span>
                            {% endif %}
                        </td>
                        <td>{{ document.prodDraw }}</td>
                        <td>{{ document.prodDrawRev }}</td>
                        <td>{{ document.refTitleFra }}</td>
                        <td>{{ document.action }}</td>
                        <td>{{ document.getInventoryImpact }}</td>
                        <td> 
                            {{ document.docType }}
                            {% if document.internalMachRec == 1 %}<img title="In house manufacturing preferred" src="{{ asset('scm.png') }}" alt="scm" style="height: 15px;">{% endif %}
                        </td>
                        <td class="p-0">
                            <p class="mb-0"><small>{{ document.prisDans1 }}</small></p>
                            <p class="mb-0"><small>{{ document.prisDans2 }}</small></p>
                        </td>
                        <td class="p-0">
                            <table class="table table-striped table-bordered mb-0 text-center" >
                                <tbody>
                                    <tr>
                                        <td class="p-1" style="font-size: 0.70rem;"><strong>Mat Type</strong> {{ document.matProdType }}</td>
                                        <td class="p-1" style="font-size: 0.70rem;"><strong>Proc Type</strong> {{ document.procType }}</td>
                                    </tr>
                                    <tr>
                                        <td class="p-1" style="font-size: 0.70rem;"><strong>Unit</strong> {{ document.Unit }}</td>
                                        <td class="p-1" style="font-size: 0.70rem;"><strong>Commodity</strong> {{ document.commodityCode }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>

                        <td class="text-center px-0">
                            {% if document.PrincipalCommentaires|length > 0 %}
                                <div class="tooltip-container" data-bs-toggle="tooltip" data-bs-html="true" title="
                                    {% for comment in document.PrincipalCommentaires %}
                                        <p class='text-nowrap mb-0' >{{ comment }}</p>
                                    {% endfor %}
                                ">
                                    <span class="tooltip-text"><i class="fa-solid fa-user"></i></span>
                                </div>
                            {% endif %}
                        </td>
                        <td class="text-center px-0">
                            {% if document.GlobalCommentaires|length > 0 %}
                                <div class="tooltip-container" data-bs-toggle="tooltip" data-bs-html="true" title="
                                    {% for comment in document.GlobalCommentaires %}
                                        <p class='text-nowrap mb-0' >{{ comment }}</p>
                                    {% endfor %}
                                ">
                                    <span class="tooltip-text"><i class="fa-solid fa-users"></i></span>
                                </div>
                            {% endif %}
                        </td>
                        <td class="text-center px-0">
                            {% if document.CurrentStepsVisa|length > 1 %}
                                <div class="dropdown">
                                    <span id="dropdownMenuButton{{ document.id }}"
                                            data-bs-toggle="dropdown"
                                            aria-expanded="false"
                                            class="badge bg-primary">
                                            {{ document.CurrentStepsVisa|length }}
                                    </span>
                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton{{ document.id }}">
                                        {% for step, value in document.CurrentStepsVisa %}
                                            <li><a class="dropdown-item" href="{{ path('app_document_place', {'place': step}) }}">{{ step|replace({'_': ' '})|first|upper ~ step|replace({'_': ' '})|slice(1) }}</a></li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            {% else %}
                                {% for step, value in document.CurrentStepsVisa %}
                                    <span class="badge bg-primary">{{ step }}</span>
                                {% endfor %}
                            {% endif %}
                        </td>
                        <td class="text-center px-0">
                            <span class="badge bg-secondary" onclick="showVisas({{ document.id }})"><i class="fa-solid fa-passport"></i></span>
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
        <div class="col-12 tab-pane fade" role="tabpanel" id="p-2">

            {# Table responsive avec amélioration de style #}
            <div class="table-responsive">
                <table class="table table-hover align-middle shadow-sm" id="table2">
                    <thead>
                        <tr class="entetes">
                            <th scope="col" class="text-center" colspan="2">Référence</th>
                            <th scope="col" class="text-center" colspan="2">Plan de Prod</th>
                            <th scope="col" class="text-center">Doc Type</th>
                            <th scope="col" class="text-center" colspan="2">Commentaires</th>
                            <th scope="col" class="text-left">
                                Cheminement
                            </th>
                            <th scope="col" style="text-align: center!important;">
                                <div class="d-flex justify-content-end gap-3">
                                    <span class="badge rounded-pill panier">
                                        Panier
                                    </span>
                                    <span class="badge rounded-pill old-place">
                                        Étape Validée
                                    </span>
                                    <span class="badge rounded-pill current-step">
                                        Étape Actuelle
                                    </span>
                                    <span class="badge rounded-pill visited-place">
                                        Prochaine Étape
                                    </span>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody id="table2-body">
                    {% for result in results %}
                        <tr>
                            <td class="text-center">{{ result.document.reference }}</td>
                            <td class="text-center">
                                {{ result.document.refRev }}                            
                                {% if result.document.ex != 'NO' %}
                                    <span style="color: red; font-weight: 500;"><sup>{{ result.document.ex }}</sup></span>
                                {% endif %}
                            </td>
                            <td class="text-center">{{ result.document.prodDraw }}</td>
                            <td class="text-center">{{ result.document.prodDrawRev }}</td>
                            <td class="text-center type">
                                <p class="mb-0">{{ result.document.docType }}</p>
                                <p class="mb-0">{{ result.document.procType }}</p>
                            </td>
                            <td class="text-center px-0">
                                {% if result.document.PrincipalCommentaires|length > 0 %}
                                    <div class="tooltip-container"  data-bs-toggle="tooltip" data-bs-html="true" title="
                                        {% for comment in result.document.PrincipalCommentaires %}
                                            <p class='text-nowrap mb-0' >{{ comment }}</p>
                                        {% endfor %}
                                    ">
                                        <span class="tooltip-text"><i class="fa-solid fa-user"></i></span>
                                    </div>
                                {% endif %}
                            </td>
                            <td class="text-center px-0">
                                {% if result.document.GlobalCommentaires|length > 0 %}
                                    <div class="tooltip-container" data-bs-toggle="tooltip" data-bs-html="true" title="
                                        {% for comment in result.document.GlobalCommentaires %}
                                            <p class='text-nowrap mb-0' >{{ comment }}</p>
                                        {% endfor %}
                                    ">
                                        <span class="tooltip-text"><i class="fa-solid fa-users"></i></span>
                                    </div>
                                {% endif %}
                            </td>

                            <td class="gap-3 p-3" style="white-space: wrap;" colspan="2">
                                {% for place, val in result.oldPlaces %}
                                    <a 
                                        class="badge rounded-pill old-place" 
                                        style="text-decoration: none;">
                                           {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}
                                {% for place, val in result.paniers.oldPlaces %}
                                    <a 
                                        class="badge rounded-pill old-place panier" 
                                        style="text-decoration: none;">
                                           {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}

                                {% for place, val in result.currentSteps %}
                                    <a href="{{ path('app_document_place', { 'place': place }) }}#onlget={{ place }}&document-id={{ result.document.id }}" 
                                       class="badge rounded-pill current-step" 
                                       style="text-decoration: none;">
                                        {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}
                                {% for place, val in result.paniers.currentSteps %}
                                    <a href="{{ path('app_document_place', { 'place': place }) }}#onlget={{ place }}&document-id={{ result.document.id }}" 
                                       class="badge rounded-pill current-step panier" 
                                       style="text-decoration: none;">
                                        {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}

                                {% for place, val in result.visitedPlaces %}
                                    <a 
                                        class="badge rounded-pill visited-place" 
                                        style="text-decoration: none;">
                                            {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}

                                {% for place, val in result.paniers.visitedPlaces %}
                                    <a 
                                        class="badge rounded-pill visited-place panier" 
                                        style="text-decoration: none;">
                                            {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>


        </div>
    </div>
</div>

<div class="modal fade" id="modalVisas" tabindex="-1" aria-labelledby="modalVisasLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-lg">
        <div class="modal-content custom-modal-content">
            <div class="modal-header custom-modal-header">
                <h5 class="modal-title" id="modalVisasLabel">Historique des visas</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                        aria-label="Close"></button>
            </div>
            <div class="modal-body">
            </div>
        </div>
    </div>
</div>


<script>
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]')
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl, {
        customClass: 'custom-tooltip'
    }))


function showVisas(documentId) {
        console.log(documentId);
        $.ajax({
            url: '{{ path("document_visas", {"id": "ID_PLACEHOLDER"}) }}'.replace('ID_PLACEHOLDER', documentId),
            type: 'GET',
            success: function(data) {
                let modalBody = document.querySelector('#modalVisas .modal-body');
                modalBody.innerHTML = '';
                if (data.length === 0) {
                    modalBody.innerHTML = '<p>Aucun visa pour ce document.</p>';
                } else {
                    let table = '<table class="table table-sm table-bordered" id="modal-visa">' +
                                '<thead><tr>' +
                                '<th class="text-center">Nom du visa</th>' +
                                '<th class="text-center">Signé par</th>' +
                                '<th class="text-center">Date de signature</th>' +
                                '</tr></thead>' +
                                '<tbody>';
                    data.forEach(visa => {
                        table += '<tr>' +
                                '<td class="text-center">' + visa.name + '</td>' +
                                '<td class="text-center">' + visa.signer + '</td>' +
                                '<td class="text-center">' + visa.dateVisa + '</td>' +
                                '</tr>';
                    });
                    table += '</tbody></table>';
                    modalBody.innerHTML = table;
                }

                // On ouvre la modale
                let myModal = new bootstrap.Modal(document.getElementById('modalVisas'));
                myModal.show();
            },
            error: function(err) {
                alert("Erreur lors de la récupération des visas");
            }
        });
    }
</script>

{% endblock %}
